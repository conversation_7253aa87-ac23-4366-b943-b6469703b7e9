<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);

require_once('includes/script.php');  
require_once('session/Login.php'); 

$model = new Dashboard();
$session = new AdministratorSession();
$session->LoginSession();

if(!isset($_SESSION['official_username']) && !isset($_SESSION['official_password']) && !isset($_SESSION['official_id'])){
    header("location:index.php?utm_campaign=expired");
}

$model = new Dashboard();
$password = $_SESSION['official_password'];
$username = $_SESSION['official_username'];
$uid = $_SESSION['official_id'];

$connection = $model->TemporaryConnection();

$query = $model->GetAdministrator($username, $password);
$admin = mysqli_fetch_assoc($query);
$id = $admin['id'];
$firstname = $admin['firstname'];
$lastname = $admin['lastname'];
$photo = $admin['photo'];
$create = $admin['created_on'];
$generate = '';
$stat = '';

// Function to fetch employees from API
function fetchApiEmployees($page = 1, $per_page = 50) {
    // Ensure per_page is at least 1 to avoid division by zero
    $per_page = max(1, (int)$per_page);
    $page = max(1, (int)$page);

    $apiUrl = "http://196.189.151.125:8080/api/HRMAPI/get_employee?page=$page&pageSize=$per_page";
    
    $response = false;
    // Check if cURL is available
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Add timeout to cURL as well
        $response = curl_exec($ch);
        
        if(curl_errno($ch)) {
            error_log("cURL Error for $apiUrl: " . curl_error($ch));
            $response = false; // Indicate failure
        }
        curl_close($ch);
    } else {
        // Fallback to file_get_contents if cURL is not available
        error_log("cURL not available, attempting file_get_contents for $apiUrl");
        $context = stream_context_create([
            'http' => [
                'ignore_errors' => true,
                'timeout' => 30
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        if ($response === false) {
            error_log("Error fetching API data using file_get_contents for $apiUrl");
            $response = false; // Indicate failure
        }
    }

    error_log("Raw API response from $apiUrl: " . ($response === false ? 'Failed to get response' : $response));

    $data = [];
    $total = 0;
    $current_page = $page;
    $total_pages = 1; // Default to 1 page for empty data

    if ($response !== false) {
        $decoded_data = json_decode($response, true);
        error_log("Decoded API data from $apiUrl: " . print_r($decoded_data, true));
        if ($decoded_data && isset($decoded_data['data']) && is_array($decoded_data['data'])) {
            $data = $decoded_data['data'];
            $total = $decoded_data['total'] ?? 0;
            $current_page = $decoded_data['page'] ?? $page;
            $per_page_from_api = $decoded_data['pageSize'] ?? $per_page;
            
            // Ensure per_page_from_api is at least 1 for calculation
            $per_page_from_api = max(1, (int)$per_page_from_api);

            $total_pages = ($total > 0) ? ceil($total / $per_page_from_api) : 1;
            
        } else {
            error_log("Invalid API response format or missing 'data' key from $apiUrl: " . ($response ?: 'Empty Response'));
        }
    } else {
        error_log("Failed to get response from API for URL: " . $apiUrl);
    }
    
    return [
        'data' => $data,
        'total' => $total,
        'current_page' => $current_page,
        'per_page' => $per_page, // Use the requested per_page, or the default
        'total_pages' => $total_pages
    ];
}

// Handle preview request
$preview_data = null;
if(isset($_GET['preview'])) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = isset($_GET['per_page']) ? (int)$_GET['per_page'] : 50;
    $preview_data = fetchApiEmployees($page, $per_page);
}

// Handle sync operation
if(isset($_POST['sync_employees'])) {
    $apiEmployees = fetchApiEmployees(1, PHP_INT_MAX)['data'];
    
    if(!empty($apiEmployees)) {
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;
        $departmentMap = array(); // To store department_id => database_id mapping
        
        // First pass: Create departments
        foreach($apiEmployees as $employee) {
            if(isset($employee['department_id']) && isset($employee['department_name'])) {
                $dept_id = (int)$employee['department_id'];
                $dept_name = mysqli_real_escape_string($connection, $employee['department_name']);
                
                // Check if department already exists
                $checkDeptQuery = "SELECT id FROM departments WHERE id = $dept_id";
                $deptResult = mysqli_query($connection, $checkDeptQuery);
                
                if(mysqli_num_rows($deptResult) == 0) {
                    // Create department
                    $insertDeptQuery = "INSERT INTO departments (id, name) VALUES ($dept_id, '$dept_name')";
                    if(!mysqli_query($connection, $insertDeptQuery)) {
                        error_log("Error creating department: " . mysqli_error($connection));
                    }
                }
            }
        }
        
        // Second pass: Create employees
        foreach($apiEmployees as $employee) {
            if(!isset($employee['employee_id']) || !isset($employee['first_name'])) {
                $skippedCount++;
                continue;
            }

            $employee_id = mysqli_real_escape_string($connection, $employee['employee_id']);
            $checkQuery = "SELECT id FROM employees WHERE employee_id = '$employee_id'";
            $result = mysqli_query($connection, $checkQuery);
            
            if(mysqli_num_rows($result) == 0) {
                $first_name = mysqli_real_escape_string($connection, $employee['first_name']);
                $middle_name = isset($employee['middle_name']) ? mysqli_real_escape_string($connection, $employee['middle_name']) : '';
                $last_name = isset($employee['last_name']) ? mysqli_real_escape_string($connection, $employee['last_name']) : '';
                $position_id = isset($employee['position_id']) ? (int)$employee['position_id'] : 0;
                $department_id = isset($employee['department_id']) ? (int)$employee['department_id'] : 'NULL';
                
                $fullname = trim($first_name . ' ' . $middle_name . ' ' . $last_name);
                
                $insertQuery = "INSERT INTO employees (employee_id, fullname, position_id, department_id, created_on) 
                               VALUES ('$employee_id', '$fullname', $position_id, $department_id, NOW())";
                
                if(!mysqli_query($connection, $insertQuery)) {
                    $errorCount++;
                    $stat = '<div class="alert alert-danger alert-dismissible">
                            <button type="button" class="close" data-dismiss="alert"></button>
                            Error syncing employees: ' . mysqli_error($connection) . '
                            </div>';
                } else {
                    $successCount++;
                }
            } else {
                $skippedCount++;
            }
        }
        
        if(empty($stat)) {
            $stat = '<div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert"></button>
                    Employees successfully synced from API. Added: ' . $successCount . ', Errors: ' . $errorCount . ', Skipped: ' . $skippedCount . '
                    </div>';
        }
    } else {
        $stat = '<div class="alert alert-warning alert-dismissible">
                <button type="button" class="close" data-dismiss="alert"></button>
                No employees found in API response. Please check the API connection.
                </div>';
    }
}

if(isset($_GET['edit'])){
    $generate = $_GET['edit'];
}

if($generate == '1' ){
    $stat = '<div class="alert alert-success alert-dismissible">
    <button type="button" class="close" data-dismiss="alert"></button>
    Employee profile successfully edited.
    </div>';
} else { }        
?>
<!doctype html>
<html lang="en" dir="ltr">
  <head>
    <title>Profiling and Payroll Management System</title>
  </head>
  <body class="" v-on:click="Reload">
    <div class="page" id="app">
      <div class="page-main">
        <div class="header collapse d-lg-flex p-0" id="headerMenuCollapse">
          <div class="container">
            <div class="row align-items-center">
              <div class="d-flex">
                <?php require_once('includes/header.php') ?>
              </div>
              <div class="col-lg order-lg-first">
                <?php require_once('includes/subheader.php') ?>
              </div>
            </div>
          </div>
        </div>
        <div class="my-3 my-md-5">
          <div class="container">
            <?php echo $stat ?>
            <div class="page-header">
              <h1 class="page-title">
                Profiling
              </h1>
            </div>
            <div class="row row-cards">           
              <div style="padding-left: 12px; padding-bottom: 25px;">
                <button type="button" class="btn btn-secondary" data-toggle="modal" data-target="#modal-add-employee">
                   <i class="fe fe-plus mr-2"></i> Add Employee
                </button>
                <a href="?preview=1" class="btn btn-info">
                    <i class="fe fe-eye mr-2"></i> Preview API Data
                </a>
                <form method="POST" style="display: inline;">
                    <button type="submit" name="sync_employees" class="btn btn-primary">
                        <i class="fe fe-refresh-cw mr-2"></i> Sync with API
                    </button>
                </form>
              </div>    

              <?php if($preview_data): ?>
              <div class="col-12">
                <div class="card">
                  <div class="card-header py-3">
                    <h3 class="card-title">API Data Preview (Total: <?php echo $preview_data['total']; ?> records)</h3>
                    <div class="card-options">
                      <form method="GET" class="d-inline-block">
                        <input type="hidden" name="preview" value="1">
                        <input type="hidden" name="page" value="1">
                        <select name="per_page" class="form-control" onchange="this.form.submit()">
                          <option value="10" <?php echo ($preview_data['per_page'] ?? 0) == 10 ? 'selected' : ''; ?>>10 per page</option>
                          <option value="25" <?php echo ($preview_data['per_page'] ?? 0) == 25 ? 'selected' : ''; ?>>25 per page</option>
                          <option value="50" <?php echo ($preview_data['per_page'] ?? 0) == 50 ? 'selected' : ''; ?>>50 per page</option>
                          <option value="100" <?php echo ($preview_data['per_page'] ?? 0) == 100 ? 'selected' : ''; ?>>100 per page</option>
                          <option value="250" <?php echo ($preview_data['per_page'] ?? 0) == 250 ? 'selected' : ''; ?>>250 per page</option>
                          <option value="500" <?php echo ($preview_data['per_page'] ?? 0) == 500 ? 'selected' : ''; ?>>500 per page</option>
                        </select>
                      </form>
                    </div>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hovered" width="100%" cellspacing="0">
                        <thead>
                          <tr>
                            <th>Employee ID</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Department</th>
                            <th>Business Unit</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php foreach($preview_data['data'] as $employee): ?>
                          <tr>
                            <td><?php echo htmlspecialchars($employee['employee_id'] ?? ''); ?></td>
                            <td>
                              <?php 
                                echo htmlspecialchars(($employee['first_name'] ?? '') . ' ' . 
                                    ($employee['middle_name'] ?? '') . ' ' . 
                                    ($employee['last_name'] ?? ''));
                              ?>
                            </td>
                            <td><?php echo htmlspecialchars($employee['position_id'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($employee['department_name'] ?? ''); ?></td>
                            <td><?php echo htmlspecialchars($employee['businessUnit_name'] ?? ''); ?></td>
                          </tr>
                          <?php endforeach; ?>
                        </tbody>
                      </table>
                    </div>
                    <?php if(($preview_data['total_pages'] ?? 0) > 1): ?>
                    <div class="mt-3">
                      <nav>
                        <ul class="pagination justify-content-center">
                          <?php if(($preview_data['current_page'] ?? 0) > 1): ?>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=1&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">First</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['current_page'] ?? 1) - 1; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Previous</a>
                          </li>
                          <?php endif; ?>
                          
                          <?php
                          $start_page = max(1, ($preview_data['current_page'] ?? 1) - 2);
                          $end_page = min(($preview_data['total_pages'] ?? 1), $start_page + 4);
                          if(($end_page - $start_page) < 4) {
                              $start_page = max(1, ($end_page ?? 1) - 4);
                          }
                          ?>
                          
                          <?php for($i = $start_page; $i <= $end_page; $i++): ?>
                          <li class="page-item <?php echo $i == ($preview_data['current_page'] ?? 0) ? 'active' : ''; ?>">
                            <a class="page-link" href="?preview=1&page=<?php echo $i; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>"><?php echo $i; ?></a>
                          </li>
                          <?php endfor; ?>
                          
                          <?php if(($preview_data['current_page'] ?? 0) < ($preview_data['total_pages'] ?? 0)): ?>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['current_page'] ?? 1) + 1; ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Next</a>
                          </li>
                          <li class="page-item">
                            <a class="page-link" href="?preview=1&page=<?php echo ($preview_data['total_pages'] ?? 1); ?>&per_page=<?php echo ($preview_data['per_page'] ?? 50); ?>">Last</a>
                          </li>
                          <?php endif; ?>
                        </ul>
                      </nav>
                      <div class="text-center mt-2">
                        Showing <?php echo ((($preview_data['current_page'] ?? 1) - 1) * ($preview_data['per_page'] ?? 50)) + 1; ?> to 
                        <?php echo min((($preview_data['current_page'] ?? 1) * ($preview_data['per_page'] ?? 50)), ($preview_data['total'] ?? 0)); ?> 
                        of <?php echo ($preview_data['total'] ?? 0); ?> entries
                      </div>
                    </div>
                    <?php endif; ?>
                  </div>
                </div>
              </div>
              <?php endif; ?>
                                    
              <div class="col-12">
                <div class="card">
                  <div class="card-header py-3">
                    <h3 class="card-title">Employee Profiling</h3>
                  </div>
                  <?php require_once('modals/modal_add_employee.php') ?>
                  <div class="card-body">
                    <div class="table-responsive">
                      <table class="table table-hovered" id="dataTable" width="100%" cellspacing="0">
                        <thead>
                          <tr>
                            <th class="w-1" >ID</th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Address</th>
                            <th>Schedule</th>
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <?php
                                $query = "SELECT *, employees.id AS empid FROM employees LEFT JOIN position ON position.id=employees.position_id LEFT JOIN schedules ON schedules.id=employees.schedule_id";
                                $result = mysqli_query($connection, $query);

                                while($row = mysqli_fetch_assoc($result)) {
                           ?>
                          <tr>
                            <td><a ><?php echo $row['employee_id'] ?></a></td>
                            <td><a class="text-inherit"><?php echo $row['fullname'] ?></a></td>
                            <td>
                              <?php echo $row['description'] ?>
                            </td>
                            <td>
                              <?php echo $row['address'] ?>
                            </td>
                            <td>
                              <?php echo date('H:i A', strtotime($row['time_in_morning'])) ?> - <?php echo date('H:i A', strtotime($row['time_out_morning'])) ?> / <?php echo date('H:i', strtotime($row['time_in_afternoon'])) ?> PM - <?php echo date('H:i', strtotime($row['time_out_afternoon'])) ?> PM
                            </td>
                            <td >
                              <a href="view.php?id=<?php echo $row['employee_id'] ?>"><button class="btn btn-success btn-sm">View</button></a>
                              <a href="edit.php?id=<?php echo $row['employee_id'] ?>"><button class="btn btn-primary btn-sm">Edit</button></a>
                            </td>
                          </tr>
                          <?php } ?>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div> 
            </div>           
          </div>
        </div>
      </div>  
    <?php require_once('includes/footer.php') ?>
    </div>   

    <?php require_once('includes/datatables.php') ?>
  </body>
</html>